"""update models

Revision ID: 1a83934ad6d1
Revises: 71f5020c6470
Create Date: 2025-07-21 09:35:48.774794

"""
from alembic import op
import models as models
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1a83934ad6d1'
down_revision = '71f5020c6470'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_mcp_providers', schema=None) as batch_op:
        batch_op.alter_column('server_identifier',
               existing_type=sa.VARCHAR(length=24),
               type_=sa.String(length=64),
               existing_nullable=False)

    with op.batch_alter_table('tool_model_invokes', schema=None) as batch_op:
        batch_op.alter_column('tool_name',
               existing_type=sa.VARCHAR(length=40),
               type_=sa.String(length=128),
               existing_nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_model_invokes', schema=None) as batch_op:
        batch_op.alter_column('tool_name',
               existing_type=sa.String(length=128),
               type_=sa.VARCHAR(length=40),
               existing_nullable=False)

    with op.batch_alter_table('tool_mcp_providers', schema=None) as batch_op:
        batch_op.alter_column('server_identifier',
               existing_type=sa.String(length=64),
               type_=sa.VARCHAR(length=24),
               existing_nullable=False)

    # ### end Alembic commands ###
