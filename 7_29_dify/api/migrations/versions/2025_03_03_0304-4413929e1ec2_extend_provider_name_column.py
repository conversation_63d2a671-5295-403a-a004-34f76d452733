"""extend_provider_name_column

Revision ID: 4413929e1ec2
Revises: 08ec4f75af5e
Create Date: 2025-03-03 03:04:58.181493

"""
from alembic import op
import models as models
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4413929e1ec2'
down_revision = '08ec4f75af5e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('dataset_collection_bindings', schema=None) as batch_op:
        batch_op.alter_column('provider_name',
               existing_type=sa.VARCHAR(length=40),
               type_=sa.String(length=255),
               existing_nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('dataset_collection_bindings', schema=None) as batch_op:
        batch_op.alter_column('provider_name',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=40),
               existing_nullable=False)

    # ### end Alembic commands ###
