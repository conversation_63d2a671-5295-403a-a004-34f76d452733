"""tool oauth

Revision ID: 71f5020c6470
Revises: 4474872b0ee6
Create Date: 2025-06-24 17:05:43.118647

"""
from alembic import op
import models as models
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '71f5020c6470'
down_revision = '1c9ba48be8e4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tool_oauth_system_clients',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('plugin_id', sa.String(length=512), nullable=False),
    sa.Column('provider', sa.String(length=255), nullable=False),
    sa.Column('encrypted_oauth_params', sa.Text(), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tool_oauth_system_client_pkey'),
    sa.UniqueConstraint('plugin_id', 'provider', name='tool_oauth_system_client_plugin_id_provider_idx')
    )
    op.create_table('tool_oauth_tenant_clients',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=False),
    sa.Column('plugin_id', sa.String(length=512), nullable=False),
    sa.Column('provider', sa.String(length=255), nullable=False),
    sa.Column('enabled', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('encrypted_oauth_params', sa.Text(), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tool_oauth_tenant_client_pkey'),
    sa.UniqueConstraint('tenant_id', 'plugin_id', 'provider', name='unique_tool_oauth_tenant_client')
    )

    with op.batch_alter_table('tool_builtin_providers', schema=None) as batch_op:
        batch_op.add_column(sa.Column('name', sa.String(length=256), server_default=sa.text("'API KEY 1'::character varying"), nullable=False))
        batch_op.add_column(sa.Column('is_default', sa.Boolean(), server_default=sa.text('false'), nullable=False))
        batch_op.add_column(sa.Column('credential_type', sa.String(length=32), server_default=sa.text("'api-key'::character varying"), nullable=False))
        batch_op.drop_constraint(batch_op.f('unique_builtin_tool_provider'), type_='unique')
        batch_op.create_unique_constraint(batch_op.f('unique_builtin_tool_provider'), ['tenant_id', 'provider', 'name'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_builtin_providers', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('unique_builtin_tool_provider'), type_='unique')
        batch_op.create_unique_constraint(batch_op.f('unique_builtin_tool_provider'), ['tenant_id', 'provider'])
        batch_op.drop_column('credential_type')
        batch_op.drop_column('is_default')
        batch_op.drop_column('name')

    op.drop_table('tool_oauth_tenant_clients')
    op.drop_table('tool_oauth_system_clients')
    # ### end Alembic commands ###
