"""update type of custom_disclaimer to TEXT

Revision ID: d07474999927
Revises: f4d7ce70a7ca
Create Date: 2024-11-01 06:22:27.981398

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd07474999927'
down_revision = 'f4d7ce70a7ca'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE recommended_apps SET custom_disclaimer = '' WHERE custom_disclaimer IS NULL")
    op.execute("UPDATE sites SET custom_disclaimer = '' WHERE custom_disclaimer IS NULL")
    op.execute("UPDATE tool_api_providers SET custom_disclaimer = '' WHERE custom_disclaimer IS NULL")

    with op.batch_alter_table('recommended_apps', schema=None) as batch_op:
        batch_op.alter_column('custom_disclaimer',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.TEXT(),
               nullable=False)

    with op.batch_alter_table('sites', schema=None) as batch_op:
        batch_op.alter_column('custom_disclaimer',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.TEXT(),
               nullable=False)

    with op.batch_alter_table('tool_api_providers', schema=None) as batch_op:
        batch_op.alter_column('custom_disclaimer',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.TEXT(),
               nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_api_providers', schema=None) as batch_op:
        batch_op.alter_column('custom_disclaimer',
               existing_type=sa.TEXT(),
               type_=sa.VARCHAR(length=255),
               nullable=True)

    with op.batch_alter_table('sites', schema=None) as batch_op:
        batch_op.alter_column('custom_disclaimer',
               existing_type=sa.TEXT(),
               type_=sa.VARCHAR(length=255),
               nullable=True)

    with op.batch_alter_table('recommended_apps', schema=None) as batch_op:
        batch_op.alter_column('custom_disclaimer',
               existing_type=sa.TEXT(),
               type_=sa.VARCHAR(length=255),
               nullable=True)

    # ### end Alembic commands ###
