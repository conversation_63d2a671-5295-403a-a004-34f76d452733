"""empty message

Revision ID: 16830a790f0f
Revises: 380c6aa5a70d
Create Date: 2024-02-01 08:21:31.111119

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '16830a790f0f'
down_revision = '380c6aa5a70d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tenant_account_joins', schema=None) as batch_op:
        batch_op.add_column(sa.Column('current', sa.Bo<PERSON>an(), server_default=sa.text('false'), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tenant_account_joins', schema=None) as batch_op:
        batch_op.drop_column('current')

    # ### end Alembic commands ###
