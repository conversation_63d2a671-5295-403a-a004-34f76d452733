"""rename api provider description

Revision ID: 00bacef91f18
Revises: 8ec536f3c800
Create Date: 2024-01-07 04:07:34.482983

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '00bacef91f18'
down_revision = '8ec536f3c800'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_api_providers', schema=None) as batch_op:
        batch_op.add_column(sa.Column('description', sa.Text(), nullable=False))
        batch_op.drop_column('description_str')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_api_providers', schema=None) as batch_op:
        batch_op.add_column(sa.Column('description_str', sa.TEXT(), autoincrement=False, nullable=False))
        batch_op.drop_column('description')

    # ### end Alembic commands ###
