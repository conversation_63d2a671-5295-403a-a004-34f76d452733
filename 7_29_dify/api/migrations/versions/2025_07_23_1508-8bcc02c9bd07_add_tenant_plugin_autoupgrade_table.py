"""add_tenant_plugin_autoupgrade_table

Revision ID: 8bcc02c9bd07
Revises: 375fe79ead14
Create Date: 2025-07-23 15:08:50.161441

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8bcc02c9bd07'
down_revision = '375fe79ead14'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tenant_plugin_auto_upgrade_strategies',
    sa.Column('id', models.types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', models.types.StringUUID(), nullable=False),
    sa.Column('strategy_setting', sa.String(length=16), server_default='fix_only', nullable=False),
    sa.Column('upgrade_time_of_day', sa.Integer(), nullable=False),
    sa.Column('upgrade_mode', sa.String(length=16), server_default='exclude', nullable=False),
    sa.Column('exclude_plugins', sa.ARRAY(sa.String(length=255)), nullable=False),
    sa.Column('include_plugins', sa.ARRAY(sa.String(length=255)), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='tenant_plugin_auto_upgrade_strategy_pkey'),
    sa.UniqueConstraint('tenant_id', name='unique_tenant_plugin_auto_upgrade_strategy')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
   
    op.drop_table('tenant_plugin_auto_upgrade_strategies')
    # ### end Alembic commands ###
