identity:
  name: localtime_to_timestamp
  author: <PERSON><PERSON><PERSON>
  label:
    en_US: localtime to timestamp
    zh_Hans: 获取时间戳
description:
  human:
    en_US: A tool for localtime convert to timestamp
    zh_Hans: 获取时间戳
  llm: A tool for localtime convert to timestamp
parameters:
  - name: localtime
    type: string
    required: true
    form: llm
    label:
      en_US: localtime
      zh_Hans: 本地时间
    human_description:
      en_US: localtime, such as 2024-1-1 0:0:0
      zh_Hans: 本地时间，比如 2024-1-1 0:0:0
  - name: timezone
    type: string
    required: false
    form: llm
    label:
      en_US: Timezone
      zh_Hans: 时区
    human_description:
      en_US: Timezone, such as Asia/Shanghai
      zh_Hans: 时区，比如 Asia/Shanghai
    default: Asia/Shanghai
